<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0">
    <title>Bank of Styx - Documentation Wiki</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            min-height: 100vh;
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: calc(100vh - 40px);
            position: sticky;
            top: 20px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: calc(100vh - 40px);
            overflow-y: auto;
            overflow-x: hidden;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .logo p {
            color: #666;
            font-size: 0.9rem;
        }

        .search-container {
            position: relative;
            margin-bottom: 20px;
        }

        .search-box {
            width: 100%;
            padding: 12px 40px 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-clear-button {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(102, 126, 234, 0.1);
            border: none;
            cursor: pointer;
            padding: 4px;
            border-radius: 50%;
            width: 22px;
            height: 22px;
            display: none;
            align-items: center;
            justify-content: center;
            color: #667eea;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            opacity: 0.7;
        }

        .search-clear-button:hover {
            background: rgba(102, 126, 234, 0.2);
            opacity: 1;
            transform: translateY(-50%) scale(1.1);
        }

        .search-clear-button:active {
            transform: translateY(-50%) scale(0.95);
        }

        .search-clear-button.visible {
            display: flex;
            animation: fadeInScale 0.2s ease-out;
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: translateY(-50%) scale(0.8);
            }
            to {
                opacity: 0.7;
                transform: translateY(-50%) scale(1);
            }
        }

        .nav-section {
            margin-bottom: 15px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            overflow: hidden;
        }

        .nav-section-header {
            color: #333;
            font-size: 1rem;
            margin: 0;
            padding: 12px 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-bottom: 1px solid #e1e5e9;
            cursor: pointer;
            user-select: none;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-section-header:hover {
            background: linear-gradient(135deg, #e9ecef, #dee2e6);
        }

        .nav-section-header .toggle-icon {
            font-size: 0.8rem;
            transition: transform 0.3s ease;
        }

        .nav-section.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .nav-section-content {
            padding: 10px;
            transition: all 0.3s ease;
            max-height: 1000px;
            overflow: hidden;
        }

        .nav-section.collapsed .nav-section-content {
            max-height: 0;
            padding: 0 10px;
        }

        .nav-item {
            display: block;
            padding: 8px 12px;
            color: #555;
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 3px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .nav-item:hover {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateX(5px);
        }

        .nav-item.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .content-header {
            border-bottom: 3px solid #e1e5e9;
            padding-bottom: 15px;
            margin-bottom: 25px;
        }

        .content-header h1 {
            color: #333;
            font-size: 2.2rem;
            margin-bottom: 10px;
        }

        .content-header .breadcrumb {
            color: #666;
            font-size: 0.9rem;
        }

        .content-body {
            line-height: 1.8;
        }

        .content-body h2 {
            color: #333;
            font-size: 1.6rem;
            margin: 25px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #e1e5e9;
        }

        .content-body h3 {
            color: #444;
            font-size: 1.3rem;
            margin: 20px 0 10px 0;
        }

        .content-body p {
            margin-bottom: 15px;
            color: #555;
        }

        .content-body ul, .content-body ol {
            margin: 15px 0 15px 25px;
        }

        .content-body li {
            margin-bottom: 8px;
            color: #555;
        }

        .content-body code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9em;
            color: #e83e8c;
        }

        .content-body pre {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            overflow-x: auto;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .content-body pre code {
            background: none;
            padding: 0;
            color: #333;
        }

        .content-body blockquote {
            border-left: 4px solid #667eea;
            padding-left: 20px;
            margin: 20px 0;
            font-style: italic;
            color: #666;
        }

        .content-body a {
            color: #667eea;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: all 0.3s ease;
        }

        .content-body a:hover {
            border-bottom-color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #fee;
            border: 1px solid #fcc;
            border-radius: 8px;
            padding: 15px;
            color: #c33;
            margin: 20px 0;
        }

        /* Mobile hamburger menu styles */
        .mobile-header {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .mobile-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .mobile-logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .mobile-logo h1 {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }

        .hamburger-button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            gap: 4px;
            width: 32px;
            height: 32px;
            justify-content: center;
            align-items: center;
        }

        .hamburger-button:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .hamburger-line {
            width: 20px;
            height: 2px;
            background: #667eea;
            transition: all 0.3s ease;
            border-radius: 1px;
        }

        .hamburger-button.active .hamburger-line:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }

        .hamburger-button.active .hamburger-line:nth-child(2) {
            opacity: 0;
        }

        .hamburger-button.active .hamburger-line:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }

        .mobile-nav-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .mobile-nav-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .mobile-nav {
            position: fixed;
            top: 0;
            right: -100%;
            width: 300px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            z-index: 999;
            transition: all 0.3s ease;
            overflow-y: auto;
            padding: 20px;
            box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
        }

        .mobile-nav.active {
            right: 0;
        }

        .mobile-nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e1e5e9;
        }

        .mobile-nav-title {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }

        .mobile-close-button {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            color: #667eea;
        }

        .mobile-close-button:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .mobile-search-container {
            position: relative;
            margin-bottom: 20px;
        }

        .mobile-search-box {
            width: 100%;
            padding: 12px 40px 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .mobile-search-box:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                gap: 0;
                padding: 10px;
            }

            .sidebar {
                display: none;
            }

            .mobile-header {
                display: block;
            }

            .main-content {
                padding: 15px;
                margin-top: 0;
                height: auto;
                min-height: calc(100vh - 120px);
                border-radius: 10px;
            }

            .content-header h1 {
                font-size: 1.8rem;
            }

            .content-body {
                font-size: 0.95rem;
                line-height: 1.7;
            }

            .content-body h2 {
                font-size: 1.4rem;
            }

            .content-body h3 {
                font-size: 1.2rem;
            }

            .search-result-item {
                padding: 12px;
                margin-bottom: 12px;
            }

            .search-results {
                padding: 15px;
                margin: 15px 0;
            }

            .collapsible-header {
                padding: 12px 15px;
            }

            .collapsible-content {
                padding: 15px;
            }

            /* Make code blocks more mobile-friendly */
            .content-body pre {
                padding: 15px;
                font-size: 0.8rem;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            /* Improve touch targets */
            .nav-item {
                padding: 12px 15px;
                font-size: 0.95rem;
            }

            .nav-section-header {
                padding: 15px;
                font-size: 1.05rem;
            }
        }

        .file-tree {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.85em;
            overflow-x: auto;
        }

        .collapsible-section {
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            margin: 20px 0;
            overflow: hidden;
        }

        .collapsible-header {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 15px 20px;
            cursor: pointer;
            user-select: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            border-bottom: 1px solid #e1e5e9;
        }

        .collapsible-header:hover {
            background: linear-gradient(135deg, #e9ecef, #dee2e6);
        }

        .collapsible-header h3 {
            margin: 0;
            color: #333;
            font-size: 1.2rem;
        }

        .collapsible-toggle {
            font-size: 1.2rem;
            transition: transform 0.3s ease;
            color: #667eea;
        }

        .collapsible-section.collapsed .collapsible-toggle {
            transform: rotate(-90deg);
        }

        .collapsible-content {
            padding: 20px;
            transition: all 0.3s ease;
            max-height: 2000px;
            overflow: hidden;
        }

        .collapsible-section.collapsed .collapsible-content {
            max-height: 0;
            padding: 0 20px;
        }

        .highlight {
            background: yellow;
            padding: 1px 3px;
            border-radius: 3px;
        }

        /* Custom scrollbar styles */
        .sidebar::-webkit-scrollbar,
        .main-content::-webkit-scrollbar {
            width: 8px;
        }

        .sidebar::-webkit-scrollbar-track,
        .main-content::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }

        .sidebar::-webkit-scrollbar-thumb,
        .main-content::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.5);
            border-radius: 4px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover,
        .main-content::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.7);
        }

        /* Search results styles */
        .search-results {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .search-results-header {
            margin-bottom: 15px;
            color: #333;
        }

        .search-result-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-result-item:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .search-result-file {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }

        .search-result-line {
            color: #333;
            margin-bottom: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.85rem;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
        }

        .search-result-context {
            color: #666;
            font-size: 0.8rem;
            border-left: 2px solid #e1e5e9;
            padding-left: 10px;
            margin-top: 8px;
        }

        .search-match-highlight {
            background: #ffeb3b;
            padding: 1px 3px;
            border-radius: 3px;
            font-weight: bold;
        }

        .search-loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .search-no-results {
            text-align: center;
            padding: 30px;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <!-- Mobile Header (visible only on mobile) -->
    <div class="mobile-header">
        <div class="mobile-header-content">
            <div class="mobile-logo">
                <span>🏛️</span>
                <h1>Bank of Styx</h1>
            </div>
            <button class="hamburger-button" id="hamburgerButton">
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
            </button>
        </div>
    </div>

    <!-- Mobile Navigation Overlay -->
    <div class="mobile-nav-overlay" id="mobileNavOverlay"></div>

    <!-- Mobile Navigation Menu -->
    <div class="mobile-nav" id="mobileNav">
        <div class="mobile-nav-header">
            <h2 class="mobile-nav-title">📚 Documentation</h2>
            <button class="mobile-close-button" id="mobileCloseButton">×</button>
        </div>

        <div class="mobile-search-container">
            <input type="text" class="mobile-search-box" placeholder="Search documentation..." id="mobileSearchBox">
            <button class="search-clear-button" id="mobileClearButton" title="Clear search">×</button>
        </div>

        <nav id="mobileNavigation">
            <!-- Mobile navigation will be populated by JavaScript -->
        </nav>
    </div>

    <div class="container">
        <aside class="sidebar">
            <div class="logo">
                <h1>🏛️ Bank of Styx</h1>
                <p>Documentation Wiki</p>
            </div>

            <div class="search-container">
                <input type="text" class="search-box" placeholder="Search documentation..." id="searchBox">
                <button class="search-clear-button" id="clearButton" title="Clear search">×</button>
            </div>

            <nav id="navigation">
                <!-- Navigation will be populated by JavaScript -->
            </nav>
        </aside>

        <main class="main-content">
            <div id="content">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Loading documentation...</p>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Documentation structure and file mappings
        const docStructure = {
            'Overview': {
                'README.md': 'Project Overview',
                'CLAUDE.md': 'Claude Documentation'
            },
            'Core Systems': {
                'docs/features/admin-dashboard-system.md': '🏛️ Administration System',
                'docs/features/banking-system.md': '🏦 Banking System',
                'docs/features/ship-management-system.md': '⚓ Ship Management',
                'docs/features/news-content-management-system.md': '📰 News & Content',
                'docs/features/volunteer-system.md': '🎯 Volunteer System',
                'docs/features/shopping-sales-system.md': '🛒 Shopping & Sales',
                'docs/features/real-time-notification-system.md': '🔔 Notifications'
            },
            'User Management': {
                'docs/features/authentication-system.md': '👤 Authentication',
                'docs/features/user-settings-profile-management-system.md': '⚙️ User Settings',
                'docs/features/support-system.md': '🎫 Support System'
            },
            'Developer Resources': {
                'docs/api/README.md': '🏗️ API Structure',
                'docs/components/ui-library.md': '🧩 Component Library',
                'docs/architecture/directory-structure.md': '🗂️ Directory Structure',
                'docs/development/setup-guide.md': '🔧 Development Guide',
                'docs/database/schema-documentation.md': '📜 Database Schema',
                'docs/technical/system-utilities.md': '🛠️ System Utilities',
                'docs/technical/upload-system.md': '📤 Upload System'
            },
            'Documentation': {
                'docs/README.md': '📚 Documentation System',
                'docs/TEMPLATE.md': '📝 Documentation Template',
                'docs/DOCUMENTATION-CHECKLIST.md': '✅ Documentation Checklist',
                'docs/STANDARDIZATION-GUIDE.md': '📋 Standardization Guide'
            },
            'Context & Implementation': {
                'context/README.md': '📖 Context Overview',
                'context/authentication.md': '🔐 Authentication Context',
                'context/banking-system.md': '💰 Banking Context',
                'context/event-volunteer-system.md': '🎪 Event & Volunteer Context',
                'context/news-content-management-system.md': '📰 News Context',
                'context/notification-system.md': '🔔 Notification Context',
                'context/product-shopping-system.md': '🛍️ Shopping Context',
                'context/support-ticket-system.md': '🎫 Support Context',
                'context/upload-system.md': '📤 Upload Context'
            },
            'Feature Research': {
                'docs/Feature tracking/admin-dashboard-system-feature-research.md': '🏛️ Admin Dashboard Research',
                'docs/Feature tracking/authentication-system-feature-research.md': '🔐 Authentication Research',
                'docs/Feature tracking/banking-system-feature-research.md': '🏦 Banking Research',
                'docs/Feature tracking/core-infrastructure-feature-research.md': '🏗️ Core Infrastructure Research',
                'docs/Feature tracking/database-schema-migrations-feature-research.md': '📊 Database Schema Research',
                'docs/Feature tracking/events-system-feature-research.md': '🎪 Events Research',
                'docs/Feature tracking/news-content-management-system-feature-research.md': '📰 News CMS Research',
                'docs/Feature tracking/real-time-notification-system-feature-research.md': '🔔 Real-time Notifications Research',
                'docs/Feature tracking/ship-management-system-feature-research.md': '⚓ Ship Management Research',
                'docs/Feature tracking/shopping-sales-system-feature-research.md': '🛒 Shopping Sales Research',
                'docs/Feature tracking/static-pages-content-feature-research.md': '📄 Static Pages Research',
                'docs/Feature tracking/support-system-feature-research.md': '🎫 Support System Research',
                'docs/Feature tracking/system-utilities-testing-feature-research.md': '🧪 System Utilities Research',
                'docs/Feature tracking/user-settings-profile-system-feature-research.md': '⚙️ User Profile Research',
                'docs/Feature tracking/volunteer-system-feature-research.md': '🎯 Volunteer Research'
            }
        };

        let currentFile = null;
        let searchIndex = [];
        let searchTimeout = null;
        let isSearching = false;

        // Initialize the application
        function init() {
            buildNavigation();
            setupSearch();
            setupMobileNavigation();
            loadDefaultContent();
        }

        // Build the navigation menu
        function buildNavigation() {
            buildNavigationForContainer('navigation');
            buildNavigationForContainer('mobileNavigation');
        }

        // Build navigation for a specific container (desktop or mobile)
        function buildNavigationForContainer(containerId) {
            const nav = document.getElementById(containerId);
            nav.innerHTML = '';

            Object.entries(docStructure).forEach(([section, files]) => {
                const sectionDiv = document.createElement('div');
                sectionDiv.className = 'nav-section';

                // Create collapsible header
                const sectionHeader = document.createElement('div');
                sectionHeader.className = 'nav-section-header';
                sectionHeader.innerHTML = `
                    <span>${section}</span>
                    <span class="toggle-icon">▼</span>
                `;

                // Create content container
                const sectionContent = document.createElement('div');
                sectionContent.className = 'nav-section-content';

                // Add click handler for collapsing
                sectionHeader.addEventListener('click', () => {
                    sectionDiv.classList.toggle('collapsed');
                });

                Object.entries(files).forEach(([filePath, displayName]) => {
                    const link = document.createElement('a');
                    link.href = '#';
                    link.className = 'nav-item';
                    link.textContent = displayName;
                    link.onclick = (e) => {
                        e.preventDefault();
                        loadFile(filePath, displayName);
                        // Close mobile nav when item is clicked
                        if (containerId === 'mobileNavigation') {
                            closeMobileNav();
                        }
                    };
                    sectionContent.appendChild(link);

                    // Build search index only once (for desktop nav)
                    if (containerId === 'navigation') {
                        searchIndex.push({
                            path: filePath,
                            name: displayName,
                            section: section,
                            element: link,
                            sectionElement: sectionDiv
                        });
                    }
                });

                sectionDiv.appendChild(sectionHeader);
                sectionDiv.appendChild(sectionContent);
                nav.appendChild(sectionDiv);
            });
        }

        // Setup search functionality
        function setupSearch() {
            setupSearchForBox('searchBox');
            setupSearchForBox('mobileSearchBox');
        }

        // Setup search for a specific search box
        function setupSearchForBox(searchBoxId) {
            const searchBox = document.getElementById(searchBoxId);
            const clearButtonId = searchBoxId === 'searchBox' ? 'clearButton' : 'mobileClearButton';
            const clearButton = document.getElementById(clearButtonId);

            // Handle input changes
            searchBox.addEventListener('input', (e) => {
                const query = e.target.value.trim();

                // Show/hide clear button
                updateClearButtonVisibility(searchBoxId, query);

                // Clear previous timeout
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }

                // Filter navigation immediately for quick feedback
                filterNavigation(query.toLowerCase());

                // Sync search boxes
                if (searchBoxId === 'searchBox') {
                    const mobileSearchBox = document.getElementById('mobileSearchBox');
                    mobileSearchBox.value = query;
                    updateClearButtonVisibility('mobileSearchBox', query);
                } else {
                    const desktopSearchBox = document.getElementById('searchBox');
                    desktopSearchBox.value = query;
                    updateClearButtonVisibility('searchBox', query);
                }

                // Debounce content search API calls
                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        performContentSearch(query);
                    }, 300);
                } else {
                    clearSearchResults();
                }
            });

            // Handle clear button click
            clearButton.addEventListener('click', () => {
                clearSearch();
            });

            // Add placeholder text with shortcut hint
            searchBox.placeholder = 'Search all documentation content... (Ctrl+K)';
        }

        // Update clear button visibility
        function updateClearButtonVisibility(searchBoxId, query) {
            const clearButtonId = searchBoxId === 'searchBox' ? 'clearButton' : 'mobileClearButton';
            const clearButton = document.getElementById(clearButtonId);

            if (query.length > 0) {
                clearButton.classList.add('visible');
            } else {
                clearButton.classList.remove('visible');
            }
        }

        // Clear search function
        function clearSearch() {
            const searchBox = document.getElementById('searchBox');
            const mobileSearchBox = document.getElementById('mobileSearchBox');
            const clearButton = document.getElementById('clearButton');
            const mobileClearButton = document.getElementById('mobileClearButton');

            // Clear both search boxes
            searchBox.value = '';
            mobileSearchBox.value = '';

            // Hide clear buttons
            clearButton.classList.remove('visible');
            mobileClearButton.classList.remove('visible');

            // Clear filters and results
            filterNavigation('');
            clearSearchResults();

            // Clear any pending search timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
        }

        // Setup mobile navigation functionality
        function setupMobileNavigation() {
            const hamburgerButton = document.getElementById('hamburgerButton');
            const mobileNav = document.getElementById('mobileNav');
            const mobileNavOverlay = document.getElementById('mobileNavOverlay');
            const mobileCloseButton = document.getElementById('mobileCloseButton');

            // Open mobile nav
            hamburgerButton.addEventListener('click', openMobileNav);

            // Close mobile nav
            mobileCloseButton.addEventListener('click', closeMobileNav);
            mobileNavOverlay.addEventListener('click', closeMobileNav);

            // Close on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && mobileNav.classList.contains('active')) {
                    closeMobileNav();
                }
            });
        }

        // Open mobile navigation
        function openMobileNav() {
            const hamburgerButton = document.getElementById('hamburgerButton');
            const mobileNav = document.getElementById('mobileNav');
            const mobileNavOverlay = document.getElementById('mobileNavOverlay');

            hamburgerButton.classList.add('active');
            mobileNav.classList.add('active');
            mobileNavOverlay.classList.add('active');

            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }

        // Close mobile navigation
        function closeMobileNav() {
            const hamburgerButton = document.getElementById('hamburgerButton');
            const mobileNav = document.getElementById('mobileNav');
            const mobileNavOverlay = document.getElementById('mobileNavOverlay');

            hamburgerButton.classList.remove('active');
            mobileNav.classList.remove('active');
            mobileNavOverlay.classList.remove('active');

            // Restore body scroll
            document.body.style.overflow = '';
        }

        // Perform content search using the API
        async function performContentSearch(query) {
            if (isSearching) return;

            isSearching = true;
            showSearchLoading();

            try {
                const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
                const data = await response.json();

                if (response.ok) {
                    displaySearchResults(data.results, query);
                } else {
                    showSearchError(data.error || 'Search failed');
                }
            } catch (error) {
                showSearchError('Failed to search: ' + error.message);
            } finally {
                isSearching = false;
            }
        }

        // Display search results
        function displaySearchResults(results, query) {
            const content = document.getElementById('content');

            if (results.length === 0) {
                content.innerHTML = `
                    <div class="search-no-results">
                        <h2>🔍 No Results Found</h2>
                        <p>No matches found for "<strong>${escapeHtml(query)}</strong>"</p>
                        <p>Try different keywords or check the navigation for specific sections.</p>
                    </div>
                `;
                return;
            }

            // Group results by file
            const groupedResults = groupResultsByFile(results);

            let html = `
                <div class="search-results">
                    <div class="search-results-header">
                        <h2>🔍 Search Results for "${escapeHtml(query)}"</h2>
                        <p>Found ${results.length} matches in ${Object.keys(groupedResults).length} files</p>
                    </div>
            `;

            Object.entries(groupedResults).forEach(([filePath, fileResults]) => {
                const displayName = getDisplayNameForFile(filePath);
                html += `
                    <div class="search-result-item" onclick="loadFileFromSearch('${filePath}', '${displayName}')">
                        <div class="search-result-file">📄 ${displayName}</div>
                `;

                fileResults.slice(0, 3).forEach(result => {
                    const highlightedLine = result.line_content.replace(/\*\*(.*?)\*\*/g, '<span class="search-match-highlight">$1</span>');
                    html += `
                        <div class="search-result-line">
                            Line ${result.line_number}: ${highlightedLine}
                        </div>
                    `;
                });

                if (fileResults.length > 3) {
                    html += `<div class="search-result-context">... and ${fileResults.length - 3} more matches</div>`;
                }

                html += `</div>`;
            });

            html += `</div>`;
            content.innerHTML = html;
        }

        // Group search results by file
        function groupResultsByFile(results) {
            const grouped = {};
            results.forEach(result => {
                if (!grouped[result.file_path]) {
                    grouped[result.file_path] = [];
                }
                grouped[result.file_path].push(result);
            });
            return grouped;
        }

        // Get display name for a file path
        function getDisplayNameForFile(filePath) {
            // Find the display name from our doc structure
            for (const [section, files] of Object.entries(docStructure)) {
                for (const [path, displayName] of Object.entries(files)) {
                    if (path === filePath) {
                        return displayName;
                    }
                }
            }
            // Fallback to filename
            return filePath.split('/').pop().replace('.md', '').replace(/-/g, ' ');
        }

        // Load file from search results
        function loadFileFromSearch(filePath, displayName) {
            loadFile(filePath, displayName);
        }

        // Show search loading state
        function showSearchLoading() {
            const content = document.getElementById('content');
            content.innerHTML = `
                <div class="search-loading">
                    <div class="spinner"></div>
                    <p>Searching through documentation...</p>
                </div>
            `;
        }

        // Show search error
        function showSearchError(message) {
            const content = document.getElementById('content');
            content.innerHTML = `
                <div class="error">
                    <h2>Search Error</h2>
                    <p>${escapeHtml(message)}</p>
                </div>
            `;
        }

        // Clear search results
        function clearSearchResults() {
            if (currentFile) {
                // If we have a current file, reload it
                const activeItem = searchIndex.find(item => item.path === currentFile);
                if (activeItem) {
                    loadFile(currentFile, activeItem.name);
                }
            } else {
                // Otherwise show default content
                loadDefaultContent();
            }
        }



        // Filter navigation based on search query
        function filterNavigation(query) {
            const sectionsWithMatches = new Set();

            searchIndex.forEach(item => {
                const matches = item.name.toLowerCase().includes(query) ||
                               item.section.toLowerCase().includes(query);
                item.element.style.display = matches ? 'block' : 'none';

                if (matches) {
                    sectionsWithMatches.add(item.sectionElement);
                }
            });

            // Show/hide sections and expand sections with matches
            document.querySelectorAll('.nav-section').forEach(section => {
                const hasMatches = sectionsWithMatches.has(section);
                section.style.display = hasMatches ? 'block' : 'none';

                // If searching and section has matches, expand it
                if (query && hasMatches) {
                    section.classList.remove('collapsed');
                }
            });

            // If no search query, show all sections
            if (!query) {
                document.querySelectorAll('.nav-section').forEach(section => {
                    section.style.display = 'block';
                });
            }
        }

        // Load default content (README.md)
        function loadDefaultContent() {
            loadFile('README.md', 'Project Overview');
        }

        // Load and display a documentation file
        async function loadFile(filePath, displayName) {
            const content = document.getElementById('content');

            // Update active navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            const activeItem = searchIndex.find(item => item.path === filePath);
            if (activeItem) {
                activeItem.element.classList.add('active');
            }

            // Show loading state
            content.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Loading ${displayName}...</p>
                </div>
            `;

            try {
                const fileContent = await loadFileContent(filePath);
                displayContent(fileContent, displayName, filePath);
                currentFile = filePath;
            } catch (error) {
                content.innerHTML = `
                    <div class="error">
                        <h2>Error Loading File</h2>
                        <p>Could not load ${filePath}: ${error.message}</p>
                        <p>Make sure the file exists and is accessible.</p>
                    </div>
                `;
            }
        }

        // Load actual file content from the filesystem
        async function loadFileContent(filePath) {
            try {
                const response = await fetch(filePath);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const content = await response.text();
                return content;
            } catch (error) {
                // If direct fetch fails (CORS issues), try alternative approaches
                throw new Error(`Failed to load file: ${error.message}`);
            }
        }



        // Display content in the main area
        function displayContent(content, title, filePath) {
            const contentDiv = document.getElementById('content');

            // Create breadcrumb
            const pathParts = filePath.split('/');
            const breadcrumb = pathParts.length > 1 ?
                pathParts.slice(0, -1).join(' / ') + ' / ' + title :
                title;

            // Convert markdown-like content to HTML
            let htmlContent = parseMarkdown(content);

            // Make large content blocks collapsible
            htmlContent = makeContentCollapsible(htmlContent);

            contentDiv.innerHTML = `
                <div class="content-header">
                    <h1>${title}</h1>
                    <div class="breadcrumb">${breadcrumb}</div>
                </div>
                <div class="content-body">
                    ${htmlContent}
                </div>
            `;

            // Add click handlers for internal links and collapsible sections
            addInternalLinkHandlers();
            addCollapsibleHandlers();
        }

        // Make large content blocks collapsible
        function makeContentCollapsible(html) {
            // Make directory structures collapsible
            html = html.replace(
                /(<h[2-6]>.*(?:Directory|Structure|Tree|Organization|Architecture).*<\/h[2-6]>)([\s\S]*?)(?=<h[1-6]|$)/gi,
                (match, header, content) => {
                    if (content.length > 500) {
                        const headerId = 'collapse-' + Math.random().toString(36).substr(2, 9);
                        return `
                            <div class="collapsible-section" id="${headerId}">
                                <div class="collapsible-header">
                                    ${header}
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content">
                                    ${content}
                                </div>
                            </div>
                        `;
                    }
                    return match;
                }
            );

            // Make large code blocks collapsible
            html = html.replace(
                /<pre><code[^>]*>([\s\S]*?)<\/code><\/pre>/g,
                (match, code) => {
                    if (code.length > 1000) {
                        const codeId = 'code-' + Math.random().toString(36).substr(2, 9);
                        return `
                            <div class="collapsible-section" id="${codeId}">
                                <div class="collapsible-header">
                                    <h3>📄 Code Block</h3>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content">
                                    ${match}
                                </div>
                            </div>
                        `;
                    }
                    return match;
                }
            );

            return html;
        }

        // Add handlers for collapsible content sections
        function addCollapsibleHandlers() {
            document.querySelectorAll('.collapsible-header').forEach(header => {
                header.addEventListener('click', () => {
                    const section = header.parentElement;
                    section.classList.toggle('collapsed');
                });
            });
        }

        // Parse markdown content using marked.js library
        function parseMarkdown(content) {
            if (typeof marked !== 'undefined') {
                // Configure marked for better rendering
                marked.setOptions({
                    highlight: function(code, lang) {
                        // Basic syntax highlighting for common languages
                        return `<code class="language-${lang || 'text'}">${escapeHtml(code)}</code>`;
                    },
                    breaks: true,
                    gfm: true
                });
                return marked.parse(content);
            } else {
                // Fallback basic markdown parser if marked.js fails to load
                return content
                    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                    .replace(/```[\s\S]*?```/g, '<pre><code>$&</code></pre>')
                    .replace(/`([^`]+)`/g, '<code>$1</code>')
                    .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*([^*]+)\*/g, '<em>$1</em>')
                    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
                    .replace(/^\- (.+$)/gim, '<li>$1</li>')
                    .replace(/\n\n/g, '</p><p>')
                    .replace(/^(.+)$/gm, '<p>$1</p>');
            }
        }

        // Escape HTML characters
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Add handlers for internal documentation links
        function addInternalLinkHandlers() {
            const links = document.querySelectorAll('.content-body a');
            links.forEach(link => {
                const href = link.getAttribute('href');
                if (href && href.endsWith('.md')) {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        const resolvedPath = resolveInternalLink(href, currentFile);
                        if (resolvedPath) {
                            const displayName = getDisplayNameForFile(resolvedPath);
                            loadFile(resolvedPath, displayName);
                        } else {
                            showLinkResolutionError(href);
                        }
                    });
                }
            });
        }

        // Resolve internal documentation links to actual file paths
        function resolveInternalLink(href, currentFilePath) {
            // Remove leading ./ if present
            const cleanHref = href.replace(/^\.\//, '');

            // Check common link mappings first
            const mappedPath = getLinkMapping(cleanHref);
            if (mappedPath) {
                return mappedPath;
            }

            // Try exact match first
            for (const [section, files] of Object.entries(docStructure)) {
                for (const [filePath, displayName] of Object.entries(files)) {
                    if (filePath === cleanHref || filePath.endsWith('/' + cleanHref)) {
                        return filePath;
                    }
                }
            }

            // Try filename-only match
            const filename = cleanHref.split('/').pop();
            for (const [section, files] of Object.entries(docStructure)) {
                for (const [filePath, displayName] of Object.entries(files)) {
                    if (filePath.endsWith('/' + filename) || filePath === filename) {
                        return filePath;
                    }
                }
            }

            // Try resolving relative to current file's directory
            if (currentFilePath) {
                const currentDir = currentFilePath.split('/').slice(0, -1).join('/');
                const resolvedPath = currentDir ? `${currentDir}/${cleanHref}` : cleanHref;

                for (const [section, files] of Object.entries(docStructure)) {
                    for (const [filePath, displayName] of Object.entries(files)) {
                        if (filePath === resolvedPath) {
                            return filePath;
                        }
                    }
                }

                // Also try with link mappings in the same directory
                const mappedInDir = getLinkMapping(cleanHref);
                if (mappedInDir && mappedInDir.startsWith(currentDir)) {
                    return mappedInDir;
                }
            }

            // Try common documentation patterns
            const commonPaths = [
                `docs/features/${cleanHref}`,
                `docs/${cleanHref}`,
                `context/${cleanHref}`,
                `docs/technical/${cleanHref}`,
                `docs/api/${cleanHref}`,
                `docs/architecture/${cleanHref}`,
                `docs/components/${cleanHref}`,
                `docs/database/${cleanHref}`,
                `docs/development/${cleanHref}`
            ];

            for (const testPath of commonPaths) {
                for (const [section, files] of Object.entries(docStructure)) {
                    for (const [filePath, displayName] of Object.entries(files)) {
                        if (filePath === testPath) {
                            return filePath;
                        }
                    }
                }
            }

            // Try fuzzy matching based on display names
            const searchTerm = cleanHref.replace('.md', '').replace(/-/g, ' ').toLowerCase();
            for (const [section, files] of Object.entries(docStructure)) {
                for (const [filePath, displayName] of Object.entries(files)) {
                    const normalizedDisplayName = displayName.toLowerCase()
                        .replace(/[🏛️🏦⚓📰🎯🛒🔔👤⚙️🎫🏗️🧩🗂️🔧📜🛠️📤📚📝✅📋📖🔐💰🎪🔔🛍️🎫]/g, '')
                        .trim();

                    if (normalizedDisplayName.includes(searchTerm) ||
                        searchTerm.includes(normalizedDisplayName.replace(/\s+/g, '-'))) {
                        return filePath;
                    }
                }
            }

            return null;
        }

        // Map common link patterns to actual file paths
        function getLinkMapping(href) {
            const linkMappings = {
                // Common misnamed links
                'event-management-system.md': 'docs/features/volunteer-system.md',
                'notification-system.md': 'docs/features/real-time-notification-system.md',
                'event-management.md': 'docs/features/volunteer-system.md',
                'notifications.md': 'docs/features/real-time-notification-system.md',

                // Links from docs/features/README.md
                'shopping-system.md': 'docs/features/shopping-sales-system.md',
                'news-system.md': 'docs/features/news-content-management-system.md',
                'user-settings-system.md': 'docs/features/user-settings-profile-management-system.md',

                // Common alternative names
                'shopping.md': 'docs/features/shopping-sales-system.md',
                'sales.md': 'docs/features/shopping-sales-system.md',
                'news.md': 'docs/features/news-content-management-system.md',
                'content.md': 'docs/features/news-content-management-system.md',
                'user-settings.md': 'docs/features/user-settings-profile-management-system.md',
                'settings.md': 'docs/features/user-settings-profile-management-system.md',
                'profile.md': 'docs/features/user-settings-profile-management-system.md',
                'support.md': 'docs/features/support-system.md',
                'tickets.md': 'docs/features/support-system.md',
                'help.md': 'docs/features/support-system.md',

                // Feature system links (relative to docs/features/)
                'authentication-system.md': 'docs/features/authentication-system.md',
                'banking-system.md': 'docs/features/banking-system.md',
                'shopping-sales-system.md': 'docs/features/shopping-sales-system.md',
                'volunteer-system.md': 'docs/features/volunteer-system.md',
                'admin-dashboard-system.md': 'docs/features/admin-dashboard-system.md',
                'news-content-management-system.md': 'docs/features/news-content-management-system.md',
                'real-time-notification-system.md': 'docs/features/real-time-notification-system.md',
                'ship-management-system.md': 'docs/features/ship-management-system.md',
                'support-system.md': 'docs/features/support-system.md',
                'user-settings-profile-management-system.md': 'docs/features/user-settings-profile-management-system.md',

                // API documentation links
                'api.md': 'docs/api/README.md',
                'api/README.md': 'docs/api/README.md',

                // Architecture links
                'directory-structure.md': 'docs/architecture/directory-structure.md',
                'architecture.md': 'docs/architecture/directory-structure.md',

                // Component links
                'ui-library.md': 'docs/components/ui-library.md',
                'components.md': 'docs/components/ui-library.md',

                // Database links
                'schema-documentation.md': 'docs/database/schema-documentation.md',
                'database.md': 'docs/database/schema-documentation.md',
                'schema.md': 'docs/database/schema-documentation.md',

                // Development links
                'setup-guide.md': 'docs/development/setup-guide.md',
                'setup.md': 'docs/development/setup-guide.md',
                'development.md': 'docs/development/setup-guide.md',
                'performance-optimization-guide.md': 'docs/development/performance-optimization-guide.md',
                'performance.md': 'docs/development/performance-optimization-guide.md',

                // Technical links
                'system-utilities.md': 'docs/technical/system-utilities.md',
                'utilities.md': 'docs/technical/system-utilities.md',
                'upload-system.md': 'docs/technical/upload-system.md',
                'uploads.md': 'docs/technical/upload-system.md',

                // Documentation meta links
                'TEMPLATE.md': 'docs/TEMPLATE.md',
                'template.md': 'docs/TEMPLATE.md',
                'DOCUMENTATION-CHECKLIST.md': 'docs/DOCUMENTATION-CHECKLIST.md',
                'checklist.md': 'docs/DOCUMENTATION-CHECKLIST.md',
                'STANDARDIZATION-GUIDE.md': 'docs/STANDARDIZATION-GUIDE.md',
                'standards.md': 'docs/STANDARDIZATION-GUIDE.md',

                // Context links
                'authentication.md': 'context/authentication.md',
                'banking-system.md': 'context/banking-system.md',
                'event-volunteer-system.md': 'context/event-volunteer-system.md',
                'news-content-management-system.md': 'context/news-content-management-system.md',
                'notification-system.md': 'context/notification-system.md',
                'product-shopping-system.md': 'context/product-shopping-system.md',
                'support-ticket-system.md': 'context/support-ticket-system.md',
                'upload-system.md': 'context/upload-system.md',

                // Root level links
                'README.md': 'README.md',
                'CLAUDE.md': 'CLAUDE.md'
            };

            return linkMappings[href] || null;
        }

        // Show a helpful error with suggestions
        function showLinkResolutionError(href) {
            const suggestions = findSimilarFiles(href);
            const content = document.getElementById('content');

            let html = `
                <div class="error">
                    <h2>📄 Documentation Link Not Found</h2>
                    <p>Could not find the documentation file: <code>${escapeHtml(href)}</code></p>
            `;

            if (suggestions.length > 0) {
                html += `
                    <h3>Did you mean one of these?</h3>
                    <div style="margin: 20px 0;">
                `;

                suggestions.forEach((suggestion, index) => {
                    html += `
                        <div class="search-result-item" onclick="loadFile('${suggestion.filePath}', '${escapeHtml(suggestion.displayName)}')" style="margin-bottom: 10px;">
                            <div class="search-result-file">📄 ${escapeHtml(suggestion.displayName)}</div>
                            <div style="color: #666; font-size: 0.9rem;">${suggestion.filePath}</div>
                        </div>
                    `;
                });

                html += `</div>`;
            } else {
                html += `
                    <p>No similar files were found. You can:</p>
                    <ul>
                        <li>Use the search function to find related content</li>
                        <li>Browse the navigation menu for available documentation</li>
                        <li>Check if the file exists in a different location</li>
                    </ul>
                `;
            }

            html += `
                    <button onclick="loadDefaultContent()" style="
                        background: linear-gradient(135deg, #667eea, #764ba2);
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 8px;
                        cursor: pointer;
                        margin-top: 15px;
                    ">← Back to Overview</button>
                </div>
            `;

            content.innerHTML = html;
        }

        // Find similar files based on the broken link
        function findSimilarFiles(href) {
            const suggestions = [];
            const searchTerm = href.replace('.md', '').replace(/[./]/g, '').toLowerCase();

            for (const [section, files] of Object.entries(docStructure)) {
                for (const [filePath, displayName] of Object.entries(files)) {
                    const fileName = filePath.split('/').pop().replace('.md', '').toLowerCase();
                    const normalizedDisplayName = displayName.toLowerCase();

                    // Check for partial matches
                    if (fileName.includes(searchTerm) ||
                        normalizedDisplayName.includes(searchTerm) ||
                        searchTerm.includes(fileName.replace(/-/g, ''))) {
                        suggestions.push({ filePath, displayName });
                    }
                }
            }

            // Sort by relevance (shorter names first, exact matches prioritized)
            suggestions.sort((a, b) => {
                const aScore = calculateRelevanceScore(a.filePath, searchTerm);
                const bScore = calculateRelevanceScore(b.filePath, searchTerm);
                return bScore - aScore;
            });

            return suggestions.slice(0, 5); // Return top 5 suggestions
        }

        // Calculate relevance score for file suggestions
        function calculateRelevanceScore(filePath, searchTerm) {
            const fileName = filePath.split('/').pop().replace('.md', '').toLowerCase();
            let score = 0;

            // Exact match gets highest score
            if (fileName === searchTerm) score += 100;

            // Starts with search term
            if (fileName.startsWith(searchTerm)) score += 50;

            // Contains search term
            if (fileName.includes(searchTerm)) score += 25;

            // Shorter filenames are preferred
            score += Math.max(0, 20 - fileName.length);

            return score;
        }

        // Highlight search terms in content
        function highlightSearchTerms(content, searchTerm) {
            if (!searchTerm) return content;

            const regex = new RegExp(`(${searchTerm})`, 'gi');
            return content.replace(regex, '<span class="highlight">$1</span>');
        }

        // Initialize the application when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);

        // Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                // Focus the appropriate search box based on screen size
                const isMobile = window.innerWidth <= 768;
                if (isMobile) {
                    // Open mobile nav and focus search
                    openMobileNav();
                    setTimeout(() => {
                        const mobileSearchBox = document.getElementById('mobileSearchBox');
                        mobileSearchBox.focus();
                        mobileSearchBox.select();
                    }, 100);
                } else {
                    const searchBox = document.getElementById('searchBox');
                    searchBox.focus();
                    searchBox.select();
                }
            }

            // Escape to clear search or unfocus
            if (e.key === 'Escape') {
                const searchBox = document.getElementById('searchBox');
                const mobileSearchBox = document.getElementById('mobileSearchBox');
                const activeElement = document.activeElement;

                if (activeElement === searchBox || activeElement === mobileSearchBox) {
                    if (activeElement.value) {
                        clearSearch();
                    } else {
                        activeElement.blur();
                    }
                }
            }

            // Ctrl/Cmd + / to toggle all navigation sections
            if ((e.ctrlKey || e.metaKey) && e.key === '/') {
                e.preventDefault();
                toggleAllNavigationSections();
            }
        });

        // Toggle all navigation sections
        function toggleAllNavigationSections() {
            const sections = document.querySelectorAll('.nav-section');
            const allCollapsed = Array.from(sections).every(section =>
                section.classList.contains('collapsed')
            );

            sections.forEach(section => {
                if (allCollapsed) {
                    section.classList.remove('collapsed');
                } else {
                    section.classList.add('collapsed');
                }
            });
        }
    </script>
</body>
</html>
