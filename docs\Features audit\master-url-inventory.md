# Master URL Inventory - Bank of Styx Application

**Generated:** August 8, 2025  
**Total Routes:** 279+ route files  
**Application Type:** Next.js 13.4.2 with App Router  
**User Base:** ~1000 users, 30-40 concurrent (peaks of 100)

---

## How to Use This Inventory

1. **For each URL listed below**, use the [Page Audit Research Template](./page-audit-template.md)
2. **Check the "Audit Status" column** to track your progress
3. **Follow the file location** to understand the implementation
4. **Note the required permissions** before testing access
5. **Test dynamic routes** with actual IDs from your database

---

## Public Pages (No Authentication Required)

| URL | File Location | Description | Audit Status |
|-----|---------------|-------------|--------------|
| `/` | `src/app/page.tsx` | Homepage with featured news and bank services | ✅ Complete - [Results](./homepage-audit-results.md) |
| `/about` | `src/app/about/page.tsx` | About page | ⚠️ Issues Found - [Results](./about-audit-results.md) |
| `/help` | `src/app/help/page.tsx` | Help/FAQ page | ✅ Complete - [Results](./help-audit-results.md) |
| `/rules` | `src/app/rules/page.tsx` | Community rules page | ❌ Broken - [Results](./rules-audit-results.md) |
| `/news` | `src/app/news/page.tsx` | Public news listing page | ✅ Complete - [Results](./news-main-audit-results.md) |
| `/news/[slug]` | `src/app/news/[slug]/page.tsx` | Individual news articles (e.g., `/news/welcome-pirates`) | ✅ Complete - [Results](./news-article-audit-results.md) |
| `/events` | `src/app/events/page.tsx` | Public events listing | ✅ Complete - [Results](./events-main-audit-results.md) |
| `/events/[id]` | `src/app/events/[id]/page.tsx` | Individual event details (e.g., `/events/123`) | ✅ Complete - [Results](./events-detail-audit-results.md) |
| `/volunteer` | `src/app/volunteer/page.tsx` | Public volunteer opportunities | ✅ Complete - [Results](./volunteer-main-audit-results.md) |
| `/ships` | `src/app/ships/page.tsx` | Ship listings page | ✅ Complete - [Results](./ships-main-audit-results.md) |
| `/ships/[id]` | `src/app/ships/[id]/page.tsx` | Individual ship details (e.g., `/ships/456`) | ✅ Complete - [Results](./ships-detail-audit-results.md) |
| `/ships/apply` | `src/app/ships/apply/page.tsx` | Ship application form | ✅ Complete - [Results](./ships-apply-audit-results.md) |
| `/shop` | `src/app/shop/page.tsx` | Public shop/marketplace | ✅ Complete - [Results](./shop-main-audit-results.md) |
| `/shop/products/[id]` | `src/app/shop/products/[id]/page.tsx` | Individual product pages (e.g., `/shop/products/789`) | ✅ Complete - [Results](./shop-product-detail-audit-results.md) |
| `/shop/search` | `src/app/shop/search/page.tsx` | Product search page | ✅ Complete - [Results](./shop-search-audit-results.md) |

## Authentication Routes

| URL | File Location | Description | Audit Status |
|-----|---------------|-------------|--------------|
| `/auth/discord/callback` | `src/app/auth/discord/callback/page.tsx` | Discord OAuth callback | ❌ Critical Issues - [Results](./auth-discord-callback-audit-results.md) |
| `/auth/discord/error` | `src/app/auth/discord/error/page.tsx` | Discord authentication error page | ✅ Complete - [Results](./auth-discord-error-audit-results.md) |

## User Dashboard Pages (Requires Authentication)

### Banking System
| URL | File Location | Description | Audit Status |
|-----|---------------|-------------|--------------|
| `/bank` | `src/app/bank/page.tsx` | Banking overview page | ✅ Complete - [Results](./bank-main-audit-results.md) |
| `/bank/dashboard` | `src/app/bank/dashboard/page.tsx` | Main banking dashboard | ✅ Complete - [Results](./bank-dashboard-audit-results.md) |
| `/bank/dashboard/deposit` | `src/app/bank/dashboard/deposit/page.tsx` | Make deposit page | ✅ Complete - [Results](./bank-deposit-audit-results.md) |
| `/bank/dashboard/withdraw` | `src/app/bank/dashboard/withdraw/page.tsx` | Make withdrawal page | ✅ Complete - [Results](./bank-withdraw-audit-results.md) |
| `/bank/dashboard/transfer` | `src/app/bank/dashboard/transfer/page.tsx` | Transfer funds page | ✅ Complete - [Results](./bank-transfer-audit-results.md) |
| `/bank/dashboard/donate` | `src/app/bank/dashboard/donate/page.tsx` | Make donation page | ✅ Complete - [Results](./bank-donate-audit-results.md) |
| `/bank/dashboard/pay-code` | `src/app/bank/dashboard/pay-code/page.tsx` | Pay code management | ✅ Complete - [Results](./bank-pay-code-audit-results.md) |
| `/bank/dashboard/transactions` | `src/app/bank/dashboard/transactions/page.tsx` | Transaction history | ✅ Complete - [Results](./bank-transactions-audit-results.md) |
| `/bank/dashboard/settings` | `src/app/bank/dashboard/settings/page.tsx` | Banking settings | ❌ Deleted (Page removed from codebase) |

### User Settings & Account
| URL | File Location | Description | Audit Status |
|-----|---------------|-------------|--------------|
| `/settings` | `src/app/settings/page.tsx` | User settings overview | ✅ Complete - [Results](./settings-audit-results.md) |
| `/settings/colors` | `src/app/settings/colors/page.tsx` | Color theme preferences | ✅ Complete - [Results](./settings-colors-audit-results.md) |
| `/settings/notifications` | `src/app/settings/notifications/page.tsx` | Notification preferences | ✅ Complete - [Results](./settings-notifications-audit-results.md) |

### Shopping System
| URL | File Location | Description | Audit Status |
|-----|---------------|-------------|--------------|
| `/shop/cart` | `src/app/shop/cart/page.tsx` | Shopping cart | ✅ Complete - [Results](./shop-cart-audit-results.md) |
| `/shop/checkout` | `src/app/shop/checkout/page.tsx` | Checkout process | ✅ Complete - [Results](./shop-checkout-audit-results.md) |
| `/shop/checkout/success` | `src/app/shop/checkout/success/page.tsx` | Order success page | ✅ Complete - [Results](./shop-checkout-success-audit-results.md) |
| `/shop/orders` | `src/app/shop/orders/page.tsx` | Order history | ⚠️ Issues Found - [Results](./shop-orders-audit-results.md) |
| `/shop/orders/[id]` | `src/app/shop/orders/[id]/page.tsx` | Individual order details | ⚠️ Issues Found - [Results](./shop-order-details-audit-results.md) |

## Content Management Systems

### News Management (Requires News Permissions)
| URL | File Location | Description | Audit Status |
|-----|---------------|-------------|--------------|
| `/news/dashboard` | `src/app/news/dashboard/page.tsx` | News management dashboard | ✅ Complete - [Results](./news-dashboard-audit-results.md) |
| `/news/dashboard/articles` | `src/app/news/dashboard/articles/page.tsx` | Article management | ⚠️ Issues Found - [Results](./news-articles-audit-results.md) |
| `/news/dashboard/articles/new` | `src/app/news/dashboard/articles/new/page.tsx` | Create new article | ⚠️ Issues Found - [Results](./news-articles-new-audit-results.md) |
| `/news/dashboard/articles/[id]` | `src/app/news/dashboard/articles/[id]/page.tsx` | Edit article | ✅ Complete - [Results](./news-articles-edit-audit-results.md) |
| `/news/dashboard/categories` | `src/app/news/dashboard/categories/page.tsx` | Category management | ✅ Complete - [Results](./news-categories-audit-results.md) |
| `/news/dashboard/featured` | `src/app/news/dashboard/featured/page.tsx` | Featured content management | ❌ Critical Issues - [Results](./news-featured-audit-results.md) |

### Sales/Product Management
| URL | File Location | Description | Audit Status |
|-----|---------------|-------------|--------------|
| `/sales/dashboard` | `src/app/sales/dashboard/page.tsx` | Sales dashboard | ⚠️ Issues Found - [Results](./sales-dashboard-audit-results.md) |
| `/sales/categories` | `src/app/sales/categories/page.tsx` | Product category management | ✅ Complete - [Results](./sales-categories-audit-results.md) |
| `/sales/categories/create` | `src/app/sales/categories/create/page.tsx` | Create new category | ✅ Complete - [Results](./sales-categories-create-audit-results.md) |
| `/sales/categories/[id]/edit` | `src/app/sales/categories/[id]/edit/page.tsx` | Edit category | ✅ Complete - [Results](./sales-categories-edit-audit-results.md) |
| `/sales/products` | `src/app/sales/products/page.tsx` | Product management | ⚠️ Issues Found - [Results](./sales-products-audit-results.md) |
| `/sales/products/create` | `src/app/sales/products/create/page.tsx` | Create new product | ✅ Complete - [Results](./sales-products-create-audit-results.md) |
| `/sales/products/[id]/edit` | `src/app/sales/products/[id]/edit/page.tsx` | Edit product | ✅ Complete - [Results](./sales-products-edit-audit-results.md) |
| `/sales/products/[id]/stats` | `src/app/sales/products/[id]/stats/page.tsx` | Product statistics | ✅ Complete - [Results](./sales-products-stats-audit-results.md) |
| `/sales/orders` | `src/app/sales/orders/page.tsx` | Order management | ✅ Complete - [Results](./sales-orders-audit-results.md) |
| `/sales/orders/[id]` | `src/app/sales/orders/[id]/page.tsx` | Individual order management | ✅ Complete - [Results](./sales-orders-detail-audit-results.md) |

## Role-Specific Dashboard Pages

### Admin Dashboard (Admin Role Required)
| URL | File Location | Description | Audit Status |
|-----|---------------|-------------|--------------|
| `/admin/dashboard` | `src/app/admin/dashboard/page.tsx` | Main admin dashboard | ✅ Complete - [Results](./admin-dashboard-main-audit-results.md) |
| `/admin/dashboard/users` | `src/app/admin/dashboard/users/page.tsx` | User management | ✅ Complete - [Results](./admin-dashboard-users-audit-results.md) |
| `/admin/dashboard/tickets` | `src/app/admin/dashboard/tickets/page.tsx` | Support ticket management | ✅ Complete - [Results](./admin-dashboard-tickets-audit-results.md) |
| `/admin/dashboard/tickets/[id]` | `src/app/admin/dashboard/tickets/[id]/page.tsx` | Individual ticket management | ✅ Complete - [Results](./admin-dashboard-tickets-detail-audit-results.md) |
| `/admin/dashboard/featured` | `src/app/admin/dashboard/featured/page.tsx` | Featured content management | ⚠️ Issues Found - [Results](./admin-dashboard-featured-audit-results.md) |
| `/admin/events` | `src/app/admin/events/page.tsx` | Event management | ✅ Complete - [Results](./admin-events-audit-results.md) |
| `/admin/events/new` | `src/app/admin/events/new/page.tsx` | Create new event | ✅ Complete - [Results](./admin-events-new-audit-results.md) |
| `/admin/events/[id]` | `src/app/admin/events/[id]/page.tsx` | Edit event | ✅ Complete - [Results](./admin-events-edit-audit-results.md) |
| `/admin/event-categories` | `src/app/admin/event-categories/page.tsx` | Event category management | ✅ Complete - [Results](./admin-event-categories-audit-results.md) |

### Cashier Dashboard (Cashier Role Required)
| URL | File Location | Description | Audit Status |
|-----|---------------|-------------|--------------|
| `/cashier/dashboard` | `src/app/cashier/dashboard/page.tsx` | Main cashier dashboard | ✅ Complete - [Results](./cashier-dashboard-main-audit-results.md) |
| `/cashier/dashboard/members` | `src/app/cashier/dashboard/members/page.tsx` | Member lookup | ✅ Complete - [Results](./cashier-members-audit-results.md) |
| `/cashier/dashboard/members/[id]` | `src/app/cashier/dashboard/members/[id]/page.tsx` | Individual member details | ✅ Complete - [Results](./cashier-member-details-audit-results.md) |
| `/cashier/dashboard/transactions` | `src/app/cashier/dashboard/transactions/page.tsx` | Transaction processing | ✅ Complete - [Results](./cashier-transactions-audit-results.md) |
| `/cashier/dashboard/deposits` | `src/app/cashier/dashboard/deposits/page.tsx` | Deposit management | ✅ Complete - [Results](./cashier-deposits-audit-results.md) |
| `/cashier/dashboard/withdrawals` | `src/app/cashier/dashboard/withdrawals/page.tsx` | Withdrawal processing | ⚠️ Issues Found - [Results](./cashier-withdrawals-audit-results.md) |
| `/cashier/dashboard/ledger` | `src/app/cashier/dashboard/ledger/page.tsx` | General ledger | ⚠️ Issues Found - [Results](./cashier-ledger-audit-results.md) |
| `/cashier/dashboard/statistics` | `src/app/cashier/dashboard/statistics/page.tsx` | Financial statistics | ✅ Complete - [Results](./cashier-statistics-audit-results.md) |

### Captain Dashboard (Ship Captain Role)
| URL | File Location | Description | Audit Status |
|-----|---------------|-------------|--------------|
| `/captain/dashboard` | `src/app/captain/dashboard/page.tsx` | Main captain dashboard | ✅ Complete - [Results](./captain-dashboard-main-audit-results.md) |
| `/captain/dashboard/members` | `src/app/captain/dashboard/members/page.tsx` | Ship member management | ✅ Complete - [Results](./captain-dashboard-members-audit-results.md) |
| `/captain/dashboard/invite` | `src/app/captain/dashboard/invite/page.tsx` | Invite new members | ✅ Complete - [Results](./captain-dashboard-invite-audit-results.md) |
| `/captain/dashboard/roles` | `src/app/captain/dashboard/roles/page.tsx` | Role management | ⚠️ Issues Found - [Results](./captain-dashboard-roles-audit-results.md) |
| `/captain/dashboard/settings` | `src/app/captain/dashboard/settings/page.tsx` | Ship settings | ✅ Complete - [Results](./captain-dashboard-settings-audit-results.md) |
| `/captain/dashboard/forms` | `src/app/captain/dashboard/forms/page.tsx` | Form management | ✅ Complete - [Results](./captain-dashboard-forms-audit-results.md) |

### Land Steward Pages (Land Steward Role)
| URL | File Location | Description | Audit Status |
|-----|---------------|-------------|--------------|
| `/land-steward` | `src/app/land-steward/page.tsx` | Land steward dashboard | ✅ Complete - [Results](./land-steward-dashboard-audit-results.md) |
| `/land-steward/applications` | `src/app/land-steward/applications/page.tsx` | Review applications | ✅ Complete - [Results](./land-steward-applications-audit-results.md) |
| `/land-steward/volunteer-requirements` | `src/app/land-steward/volunteer-requirements/page.tsx` | Volunteer requirement management | ❌ Critical Issues - [Results](./land-steward-volunteer-requirements-audit-results.md) |

### Volunteer System Pages
| URL | File Location | Description | Audit Status |
|-----|---------------|-------------|--------------|
| `/volunteer/dashboard` | `src/app/volunteer/dashboard/page.tsx` | Volunteer dashboard | ✅ Complete - [Results](./volunteer-dashboard-audit-results.md) |
| `/volunteer/dashboard/categories` | `src/app/volunteer/dashboard/categories/page.tsx` | Volunteer categories | ✅ Complete - [Results](./volunteer-categories-audit-results.md) |
| `/volunteer/dashboard/shifts` | `src/app/volunteer/dashboard/shifts/page.tsx` | Shift management | ⚠️ Issues Found - [Results](./volunteer-shifts-audit-results.md) |
| `/volunteer/dashboard/payments` | `src/app/volunteer/dashboard/payments/page.tsx` | Payment tracking | ✅ Complete - [Results](./volunteer-payments-audit-results.md) |
| `/volunteer/dashboard/category-lead-view` | `src/app/volunteer/dashboard/category-lead-view/page.tsx` | Category lead view | ⚠️ Issues Found - [Results](./volunteer-category-lead-view-audit-results.md) |
| `/volunteer/lead/dashboard` | `src/app/volunteer/lead/dashboard/page.tsx` | Volunteer lead dashboard | ❌ Needs Component Review - [Results](./volunteer-lead-dashboard-audit-results.md) |

## Test & Development Routes

| URL | File Location | Description | Audit Status |
|-----|---------------|-------------|--------------|
| `/test/performance` | `src/app/test/performance/page.tsx` | Performance testing page | ✅ Complete - [Results](./test-performance-audit-results.md) |
| `/test/realtime` | `src/app/test/realtime/page.tsx` | Real-time functionality testing | ✅ Complete - [Results](./test-realtime-audit-results.md) |
| `/test/upload-v2` | `src/app/test/upload-v2/page.tsx` | File upload testing | ❌ Needs Functional Testing - [Results](./test-upload-v2-audit-results.md) |

---

## Dynamic Route Testing Examples

When testing dynamic routes, use these example values from your database:

### Example IDs to Test
- **News Articles**: Use actual slugs from your database (e.g., `welcome-pirates`, `festival-announcement`)
- **Events**: Use actual event IDs (UUIDs) from your database
- **Products**: Use actual product IDs from your shop system
- **Users**: Use actual user IDs for member management pages
- **Orders**: Use actual order IDs from transaction history
- **Ships**: Use actual ship IDs for ship detail pages

### Query Parameters to Test

**News Filtering:**
```
/news?category=announcement&featured=true&limit=10
```

**Event Search:**
```
/events?category=festival&upcoming=true&sortBy=startDate
```

**Product Search:**
```
/shop/search?q=treasure&category=accessories&minPrice=10&maxPrice=100
```

**Transaction History:**
```
/bank/dashboard/transactions?type=deposit&dateFrom=2025-01-01&dateTo=2025-12-31
```

---

## Audit Progress Tracking

### Legend
- ⏳ **Pending** - Not yet audited
- 🔄 **In Progress** - Currently being audited
- ✅ **Complete** - Audit completed, no issues found
- ⚠️ **Issues Found** - Audit completed, issues identified
- ❌ **Broken** - Page/functionality not working
- 🚧 **Incomplete** - Page exists but functionality incomplete

### Overall Progress
- **Total Pages to Audit**: 80+ main pages
- **Audited**: 63 (Public pages + News System + Shopping Orders + Auth + Land Steward + Captain Dashboard + Cashier Dashboard + Admin Dashboard)
- **In Progress**: 0
- **Issues Found**: 11 (News + Shopping + Auth + Land Steward + Captain Roles + Cashier Withdrawals/Ledger + Admin Featured Content + Admin Tickets)
- **Critical Issues**: 6 (Featured content pagination + Auth token validation + Volunteer requirements access control + Withdrawal balance verification + Ledger calculation errors + Admin featured content hero banner system)
- **New Issues**: 4 (Admin featured content - hero banner placeholder system, Admin tickets - missing customer communication, Cashier withdrawals - missing balance verification, Ledger net change calculation error)

---

## Priority Auditing Order

### Phase 1 - Core Functionality (Week 1)
1. **Homepage** - `/`
2. **Authentication** - `/auth/*` routes  
3. **Banking Core** - `/bank/dashboard`, `/bank/dashboard/deposit`, `/bank/dashboard/withdraw`
4. **User Settings** - `/settings`, `/settings/colors`

### Phase 2 - User Features (Week 2)
1. **News System** - `/news`, `/news/[slug]`, `/news/dashboard/*` ✅ **COMPLETED**
2. **Events** - `/events`, `/events/[id]`
3. **Shopping** - `/shop`, `/shop/cart`, `/shop/checkout`
4. **Volunteer** - `/volunteer`, `/volunteer/dashboard/*`

### Phase 3 - Management Dashboards (Week 3)
1. **Admin Dashboard** - `/admin/dashboard/*` ✅ **COMPLETED**
2. **Cashier Dashboard** - `/cashier/dashboard/*` ✅ **COMPLETED**
3. **Sales Management** - `/sales/*`
4. **Captain Dashboard** - `/captain/dashboard/*` ✅ **COMPLETED**

### Phase 4 - Specialized Features (Week 4)
1. **Ships System** - `/ships/*`, `/land-steward/*`
2. **Advanced Features** - Complex forms, integrations
3. **Test Routes** - `/test/*` functionality
4. **Edge Cases** - Error handling, permissions

---

## Next Steps

1. **Start with Phase 1** - Focus on core functionality first
2. **Use the audit template** for each page systematically  
3. **Document issues immediately** as they're discovered
4. **Prioritize fixes** based on business impact and user frequency
5. **Re-audit** after fixes are implemented
6. **Track progress** by updating the audit status in this document

This inventory provides a comprehensive foundation for auditing every aspect of your Bank of Styx application!