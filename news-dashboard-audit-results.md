# Page Audit Research Template

## Basic Page Information
**URL:** /news/dashboard
**File Location:** web/apps/main-site/src/app/news/dashboard/page.tsx
**Date Audited:** 2025-08-08
**Audited By:** Augment Agent (Augment Code)
**Page Type:** [ ] Public [x] User Dashboard [ ] Admin [x] Role-Specific [ ] API Endpoint

---

## Page Overview
**Primary Purpose:** Provide editors a high-level overview of news content performance and quick entry points to manage articles, categories, and featured content.
**Target Users/Roles:** Editors (and likely Admins)
**Brief Description:** Dashboard displays article counts by status, total views, top articles by views, recent modifications, and quick links to create/manage content.

---

## Functionality Assessment

### Core Features Present
- [x] Overview stats (published/draft/paused counts; total views)
- [x] Top articles by views list with edit links
- [x] Recently modified list with status badges and edit links
- [x] Quick actions (Create Article, Manage Articles, Categories, Featured)

### User Interactions Available
**Forms:**
- [ ] None on this page (forms exist on Articles/Create/Edit pages)

**Buttons/Actions:**
- [x] Quick Action: Create Article (navigates to /news/dashboard/articles/new)
- [x] Quick Action: Manage Articles (navigates to /news/dashboard/articles)
- [x] Quick Action: Manage Categories (navigates to /news/dashboard/categories)
- [x] Quick Action: Manage Featured (navigates to /news/dashboard/featured)
- [x] Edit buttons on Top/Recent article items

**Navigation Elements:**
- [x] Main navigation (in NewsDashboardLayout): Dashboard, Articles, Categories
- [ ] Breadcrumbs: Missing
- [ ] Back buttons: Not present (standard for dashboards)

### Data Display
**Information Shown:**
- [x] Article status distribution (Published/Draft/Paused) — computed client-side
- [x] Category distribution (count per category) — computed client-side
- [x] Total views — sum of article views
- [x] Top 5 articles by views — title, category, views
- [x] 5 most recently modified articles — title, author, last modified, status

**Data Sources:**
- [x] Database: Prisma models newsArticle, newsCategory (via API)
- [x] API endpoints: GET /api/news/articles (for stats, recent, top)
- [ ] Static content: None

---

## Access Control & Permissions
**Required Authentication:** [x] Yes [ ] No
**Required Roles/Permissions:** Editor (UI check); Admin not granted by UI-only check
**Access Testing Results (from code analysis):**
- [x] Unauthenticated access: Blocked via client redirect to "/" (expected)
- [x] Wrong role access: Blocked via client redirect (expected)
- [x] Correct role access (editor): Working

Note: Some server endpoints used by related management pages lack server-side authorization checks (see Missing/Broken below). Also, Admin users without editor flag are blocked from the UI despite API allowing Admins to create articles.

---

## Current State Assessment

### Working Features ✅
1. Client-side auth gate using useAuth and role check
2. Stats/top/recent computations from articles data
3. Quick actions and navigation (including mobile-friendly nav)

### Broken/Non-functional Features ❌
1. Issue: Server-side authorization missing on certain News API routes (e.g., toggle featured, toggle status, categories CRUD)
   Impact: High
   Error Details: Endpoints like PUT /api/news/articles/[id]/featured and /api/news/articles/[id]/status, and POST/PUT/DELETE /api/news/categories do not validate current user/role. This enables unauthorized modifications if endpoints are called directly.

2. Issue: Admins (roles.admin=true) cannot access News Dashboard unless roles.editor=true
   Impact: Medium
   Error Details: UI guard checks only user.roles?.editor; API for creating articles permits admin or editor. Role policy inconsistent.

### Missing Features ⚠️
1. Expected Feature: Side navigation item for "Featured"
   Why Missing: Layout nav lists Dashboard/Articles/Categories only
   Impact: Low/Medium (discoverability)

2. Expected Feature: Server-side analytics endpoint usage for dashboard metrics
   Why Missing: Dashboard fetches up to 100 articles and computes metrics client-side
   Impact: Medium (performance at scale)

### Incomplete Features 🔄
1. Feature: Accessibility considerations (labels, landmarks)
   What Works: Basic semantic HTML and visible focus is not explicitly handled
   What's Missing: Accessibility audit (aria labels for SVG-only action tiles, keyboard focus order)
   Impact: Medium

---

## User Experience

### Design & Layout
- [x] Consistent with site theme
- [x] Mobile responsive (separate mobile nav/stats)
- [x] Loading states present (auth + data)
- [x] Error states handled (articlesError shows inline error)
- [ ] Accessibility considerations (needs improvement)

### Performance
- [ ] Page loads quickly (< 3 seconds) — context dependent; uses limit=100 fetch
- [x] No obvious client-side console errors in code
- [ ] Images optimized — N/A on this page (only SVGs/text)
- [ ] API calls efficient — Could leverage /api/news/analytics instead of GET /api/news/articles limit=100

### Usability Issues
1. "Featured" management not in side nav; only accessible via Quick Actions
2. No breadcrumbs for deeper subpages (Articles, Categories)
3. Stats recomputed on client from list; may be slow with large datasets

---

## Desired vs. Actual Functionality

### Requirements/Expectations
**What should this page do?**
1. Show a performant, accurate overview of news activity
2. Provide quick navigation to core management areas
3. Enforce role-based access and align with API role policy

**What user problems should it solve?**
1. Quickly understand content status and performance
2. Quickly jump to create/edit content
3. Prevent unauthorized access/modification

### Gap Analysis
**Missing functionality:**
- [ ] Nice-to-have gap 1: Use analytics endpoint for stats (server-aggregated)
- [ ] Nice-to-have gap 2: Add Featured to persistent nav

**Incorrect behavior:**
- [ ] Behavior 1: Admins cannot access dashboard UI unless editor role is also set (expected: admins OR editors)

---

## Priority Assessment

### Priority Level
- [ ] Critical (P0)
- [x] High (P1) — Security/auth gaps in related endpoints; role alignment
- [x] Medium (P2) — Navigation discoverability; performance improvement
- [ ] Low (P3)

### Business Impact
- [x] High — Unauthorized API access risk if endpoints are exposed
- [x] Medium — Affects editor/admin UX and performance at scale
- [ ] Low

### Technical Complexity (Estimated)
- [x] Simple — Add nav item; minor UI role guard fix
- [x] Moderate — Add server-side auth checks to endpoints; switch to analytics API
- [ ] Complex

---

## Action Items & Recommendations

### Immediate Fixes Required
1. Fix: Add server-side authorization to News API mutations
   Estimated Effort: 2–4 hours
   Priority: P0/P1
   Notes: For routes like /api/news/articles/[id]/featured, /status, and /api/news/categories (POST/PUT/DELETE), require getCurrentUser and userHasRole(req, 'editor') || userHasRole(req, 'admin'). Return 401/403 on failure.

2. Fix: Align UI role guard with API policy
   Estimated Effort: 15–30 minutes
   Priority: P1
   Notes: In NewsDashboardLayout and page, check roles.editor || roles.admin when gating access.

### Feature Enhancements
1. Enhancement: Use /api/news/analytics for dashboard stats
   Rationale: Reduce data transfer and client CPU; more accurate at scale
   Estimated Effort: 1–2 hours
   Priority: P2

2. Enhancement: Add "Featured" to left nav (NewsDashboardLayout)
   Rationale: Improve discoverability beyond Quick Actions
   Estimated Effort: 15–30 minutes
   Priority: P3

### Long-term Improvements
1. Improvement: Add accessibility passes (ARIA labels, focus states)
   Rationale: Improve usability and compliance
   Estimated Effort: 2–4 hours
   Priority: P3

---

## Technical Notes

### Dependencies
**Required for functionality:**
- APIs: /api/news/articles, /api/news/analytics (recommended), /api/news/categories
- Components: NewsDashboardLayout
- Services: hooks/useNews.ts; services/newsService.ts; contexts/AuthContext
- External libraries: @tanstack/react-query, next/navigation, react-hot-toast, Prisma backend via Next.js API routes

### Related Pages/Features
**Connected functionality:**
- /news/dashboard/articles — list/manage (status toggle, feature, delete)
- /news/dashboard/articles/new — create
- /news/dashboard/articles/[id] — edit
- /news/dashboard/categories — category management
- /news/dashboard/featured — featured management

### Development Considerations
**Notes for implementation:**
- Use getCurrentUser/userHasRole in all mutating news routes
- Consider pagination and server-side aggregation for large datasets
- Update BankUser.roles check in UI to include admins

---

## Screenshots/Evidence
**Visual/code evidence of issues:**
- UI role guard (editor-only):
- API toggle featured without auth (needs role checks)

---

## Additional Observations
**Other notes, edge cases, or important context:**
- GET /api/news/articles has no auth requirement (OK for internal dashboard usage, but ensure mutating routes are protected). If exposing to public, consider further restrictions.

---

## Review Checklist
Before marking this audit complete, verify:
- [x] All sections filled out completely
- [x] Priority levels assigned appropriately
- [x] Action items are specific and actionable
- [x] Business impact clearly identified
- [x] Technical complexity estimated
- [x] Related pages/dependencies noted

